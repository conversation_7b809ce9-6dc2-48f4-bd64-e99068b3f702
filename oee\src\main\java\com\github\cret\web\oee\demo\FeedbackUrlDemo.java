package com.github.cret.web.oee.demo;

import java.util.Map;

import com.github.cret.web.oee.config.FeedbackConfig;
import com.github.cret.web.oee.domain.request.FeedbackUrlParams;

/**
 * 反馈URL生成演示类
 * 演示新的加密查询参数URL结构
 */
public class FeedbackUrlDemo {

	public static void main(String[] args) {
		// 创建配置实例
		FeedbackConfig config = new FeedbackConfig();
		config.setHandleBaseUrl("http://localhost:3333/feedback/handle");
		config.setEnableUrlEncryption(true);
		config.setUrlEncryptionKey("YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXoxMjM0NTY=");

		System.out.println("=== 反馈URL加密演示 ===\n");

		// 演示基本URL生成（启用加密）
		System.out.println("1. 基本URL生成（启用加密）:");
		String editUrl = config.buildFeedbackEditUrl("trigger123", "send456");
		System.out.println("编辑URL: " + editUrl);

		String viewUrl = config.buildFeedbackViewUrl("trigger123", "send456");
		System.out.println("查看URL: " + viewUrl);

		String closeUrl = config.buildFeedbackCloseUrl("trigger123", "send456");
		System.out.println("关闭URL: " + closeUrl);

		// 演示包含用户信息的URL生成（旧方法 - 已弃用）
		System.out.println("\n2. 包含用户信息的URL生成（启用加密）- 旧方法:");
		String editUrlWithUser = config.buildFeedbackEditUrlWithUser("trigger123", "send456", "user789", "张三");
		System.out.println("编辑URL（含用户）: " + editUrlWithUser);

		String viewUrlWithUser = config.buildFeedbackViewUrlWithUser("trigger123", "send456", "user789", "张三");
		System.out.println("查看URL（含用户）: " + viewUrlWithUser);

		// 演示使用新的参数对象方法
		System.out.println("\n3. 使用参数对象的URL生成（推荐方法）:");
		FeedbackUrlParams editParams = FeedbackUrlParams.forEdit("trigger123", "send456", "user789", "张三");
		String newEditUrlWithUser = config.buildFeedbackEditUrlWithUser(editParams);
		System.out.println("编辑URL（含用户）- 新方法: " + newEditUrlWithUser);

		FeedbackUrlParams viewParams = FeedbackUrlParams.forView("trigger123", "send456", "user789", "张三");
		String newViewUrlWithUser = config.buildFeedbackViewUrlWithUser(viewParams);
		System.out.println("查看URL（含用户）- 新方法: " + newViewUrlWithUser);

		// 演示通用方法
		FeedbackUrlParams generalParams = new FeedbackUrlParams("edit", "trigger123", "send456", "user789", "张三");
		String generalUrl = config.buildFeedbackHandleUrlWithUser(generalParams);
		System.out.println("通用方法URL: " + generalUrl);

		// 演示URL解密
		System.out.println("\n4. URL参数解密演示:");
		String encryptedData = extractEncryptedData(editUrlWithUser);
		if (encryptedData != null) {
			Map<String, String> decryptedParams = config.decryptUrlParams(encryptedData);
			System.out.println("解密后的参数:");
			decryptedParams.forEach((key, value) -> System.out.println("  " + key + " = " + value));
		}

		// 演示未启用加密的情况
		System.out.println("\n4. 未启用加密的URL生成:");
		config.setEnableUrlEncryption(false);
		String plainEditUrl = config.buildFeedbackEditUrl("trigger123", "send456");
		System.out.println("明文编辑URL: " + plainEditUrl);

		String plainEditUrlWithUser = config.buildFeedbackEditUrlWithUser("trigger123", "send456", "user789", "张三");
		System.out.println("明文编辑URL（含用户）- 旧方法: " + plainEditUrlWithUser);

		// 使用新方法生成明文URL
		FeedbackUrlParams plainParams = FeedbackUrlParams.forEdit("trigger123", "send456", "user789", "张三");
		String plainNewEditUrlWithUser = config.buildFeedbackEditUrlWithUser(plainParams);
		System.out.println("明文编辑URL（含用户）- 新方法: " + plainNewEditUrlWithUser);

		// 演示加密状态检查
		System.out.println("\n5. 加密状态检查:");
		config.setEnableUrlEncryption(true);
		System.out.println("加密已启用: " + config.isUrlEncryptionEnabled());

		config.setEnableUrlEncryption(false);
		System.out.println("加密已禁用: " + config.isUrlEncryptionEnabled());

		config.setEnableUrlEncryption(true);
		config.setUrlEncryptionKey("invalid-key");
		System.out.println("无效密钥: " + config.isUrlEncryptionEnabled());

		System.out.println("\n=== 演示完成 ===");
	}

	/**
	 * 从URL中提取加密数据
	 * @param url 完整URL
	 * @return 加密数据字符串，如果没有找到则返回null
	 */
	private static String extractEncryptedData(String url) {
		if (url == null || !url.contains("?data=")) {
			return null;
		}
		int dataIndex = url.indexOf("?data=");
		return url.substring(dataIndex + 6);
	}
}
