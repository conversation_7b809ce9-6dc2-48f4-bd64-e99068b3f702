package com.github.cret.web.oee.utils;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;

/**
 * URL加密工具测试类
 */
class UrlEncryptionUtilTest {

	@Test
	void testGenerateKey() {
		String key = UrlEncryptionUtil.generateKey();
		assertNotNull(key);
		assertTrue(UrlEncryptionUtil.isValidKey(key));
	}

	@Test
	void testIsValidKey() {
		// 测试有效密钥
		String validKey = UrlEncryptionUtil.generateKey();
		assertTrue(UrlEncryptionUtil.isValidKey(validKey));

		// 测试无效密钥
		assertFalse(UrlEncryptionUtil.isValidKey(null));
		assertFalse(UrlEncryptionUtil.isValidKey(""));
		assertFalse(UrlEncryptionUtil.isValidKey("invalid-key"));
		assertFalse(UrlEncryptionUtil.isValidKey("dGVzdA==")); // 太短的密钥
	}

	@Test
	void testEncryptAndDecryptParams() {
		String secretKey = UrlEncryptionUtil.generateKey();

		// 准备测试数据
		Map<String, String> originalParams = new HashMap<>();
		originalParams.put("userId", "test123");
		originalParams.put("userName", "测试用户");
		originalParams.put("action", "edit");

		// 加密参数
		String encryptedParams = UrlEncryptionUtil.encryptParams(originalParams, secretKey);
		assertNotNull(encryptedParams);
		assertFalse(encryptedParams.isEmpty());

		// 解密参数
		Map<String, String> decryptedParams = UrlEncryptionUtil.decryptParams(encryptedParams, secretKey);
		assertNotNull(decryptedParams);
		assertEquals(originalParams.size(), decryptedParams.size());
		assertEquals(originalParams.get("userId"), decryptedParams.get("userId"));
		assertEquals(originalParams.get("userName"), decryptedParams.get("userName"));
		assertEquals(originalParams.get("action"), decryptedParams.get("action"));
	}

	@Test
	void testEncryptEmptyParams() {
		String secretKey = UrlEncryptionUtil.generateKey();

		// 测试空参数
		Map<String, String> emptyParams = new HashMap<>();
		String encryptedParams = UrlEncryptionUtil.encryptParams(emptyParams, secretKey);
		assertEquals("", encryptedParams);

		// 测试null参数
		String encryptedNullParams = UrlEncryptionUtil.encryptParams(null, secretKey);
		assertEquals("", encryptedNullParams);
	}

	@Test
	void testDecryptEmptyParams() {
		String secretKey = UrlEncryptionUtil.generateKey();

		// 测试空字符串
		Map<String, String> result1 = UrlEncryptionUtil.decryptParams("", secretKey);
		assertNotNull(result1);
		assertTrue(result1.isEmpty());

		// 测试null
		Map<String, String> result2 = UrlEncryptionUtil.decryptParams(null, secretKey);
		assertNotNull(result2);
		assertTrue(result2.isEmpty());
	}

	@Test
	void testBuildEncryptedQueryString() {
		String secretKey = UrlEncryptionUtil.generateKey();

		// 准备测试数据
		Map<String, String> params = new HashMap<>();
		params.put("userId", "test123");
		params.put("userName", "测试用户");

		// 构建加密查询字符串
		String queryString = UrlEncryptionUtil.buildEncryptedQueryString(params, secretKey);
		assertNotNull(queryString);
		assertTrue(queryString.startsWith("?data="));

		// 提取加密数据并验证解密
		String encryptedData = queryString.substring(6); // 去掉 "?data="
		Map<String, String> decryptedParams = UrlEncryptionUtil.decryptParams(encryptedData, secretKey);
		assertEquals(params, decryptedParams);
	}

	@Test
	void testBuildEncryptedQueryStringWithEmptyParams() {
		String secretKey = UrlEncryptionUtil.generateKey();

		// 测试空参数
		String queryString1 = UrlEncryptionUtil.buildEncryptedQueryString(new HashMap<>(), secretKey);
		assertEquals("", queryString1);

		// 测试null参数
		String queryString2 = UrlEncryptionUtil.buildEncryptedQueryString(null, secretKey);
		assertEquals("", queryString2);
	}

	@Test
	void testEncryptionWithDifferentKeys() {
		String key1 = UrlEncryptionUtil.generateKey();
		String key2 = UrlEncryptionUtil.generateKey();

		Map<String, String> params = new HashMap<>();
		params.put("userId", "test123");

		// 用key1加密
		String encrypted = UrlEncryptionUtil.encryptParams(params, key1);

		// 用key1解密应该成功
		Map<String, String> decrypted1 = UrlEncryptionUtil.decryptParams(encrypted, key1);
		assertEquals(params, decrypted1);

		// 用key2解密应该失败
		assertThrows(RuntimeException.class, () -> {
			UrlEncryptionUtil.decryptParams(encrypted, key2);
		});
	}

	@Test
	void testEncryptionWithInvalidKey() {
		Map<String, String> params = new HashMap<>();
		params.put("userId", "test123");

		// 使用无效密钥应该抛出异常
		assertThrows(RuntimeException.class, () -> {
			UrlEncryptionUtil.encryptParams(params, "invalid-key");
		});

		assertThrows(RuntimeException.class, () -> {
			UrlEncryptionUtil.decryptParams("some-data", "invalid-key");
		});
	}

}
