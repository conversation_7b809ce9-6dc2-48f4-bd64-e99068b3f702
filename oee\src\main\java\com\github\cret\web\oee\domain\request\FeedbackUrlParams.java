package com.github.cret.web.oee.domain.request;

import jakarta.validation.constraints.NotBlank;

/**
 * 反馈URL构建参数封装类
 */
public class FeedbackUrlParams {

	/**
	 * 操作类型（edit-编辑/填写解决方案, view-查看解决方案, close-关闭异常）
	 */
	@NotBlank(message = "操作类型不能为空")
	private String action;

	/**
	 * 触发记录ID
	 */
	@NotBlank(message = "触发记录ID不能为空")
	private String triggerRecordId;

	/**
	 * 发送记录ID
	 */
	@NotBlank(message = "发送记录ID不能为空")
	private String sendRecordId;

	/**
	 * 用户ID
	 */
	private String userId;

	/**
	 * 用户名称
	 */
	private String userName;

	/**
	 * 默认构造方法
	 */
	public FeedbackUrlParams() {
	}

	/**
	 * 构造方法（不包含用户信息）
	 */
	public FeedbackUrlParams(String action, String triggerRecordId, String sendRecordId) {
		this.action = action;
		this.triggerRecordId = triggerRecordId;
		this.sendRecordId = sendRecordId;
	}

	/**
	 * 构造方法（包含用户信息）
	 */
	public FeedbackUrlParams(String action, String triggerRecordId, String sendRecordId, String userId, String userName) {
		this.action = action;
		this.triggerRecordId = triggerRecordId;
		this.sendRecordId = sendRecordId;
		this.userId = userId;
		this.userName = userName;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getTriggerRecordId() {
		return triggerRecordId;
	}

	public void setTriggerRecordId(String triggerRecordId) {
		this.triggerRecordId = triggerRecordId;
	}

	public String getSendRecordId() {
		return sendRecordId;
	}

	public void setSendRecordId(String sendRecordId) {
		this.sendRecordId = sendRecordId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	/**
	 * 检查是否包含用户信息
	 */
	public boolean hasUserInfo() {
		return userId != null && !userId.trim().isEmpty();
	}

	/**
	 * 创建编辑类型的参数对象
	 */
	public static FeedbackUrlParams forEdit(String triggerRecordId, String sendRecordId) {
		return new FeedbackUrlParams("edit", triggerRecordId, sendRecordId);
	}

	/**
	 * 创建编辑类型的参数对象（包含用户信息）
	 */
	public static FeedbackUrlParams forEdit(String triggerRecordId, String sendRecordId, String userId, String userName) {
		return new FeedbackUrlParams("edit", triggerRecordId, sendRecordId, userId, userName);
	}

	/**
	 * 创建查看类型的参数对象
	 */
	public static FeedbackUrlParams forView(String triggerRecordId, String sendRecordId) {
		return new FeedbackUrlParams("view", triggerRecordId, sendRecordId);
	}

	/**
	 * 创建查看类型的参数对象（包含用户信息）
	 */
	public static FeedbackUrlParams forView(String triggerRecordId, String sendRecordId, String userId, String userName) {
		return new FeedbackUrlParams("view", triggerRecordId, sendRecordId, userId, userName);
	}

	/**
	 * 创建关闭类型的参数对象
	 */
	public static FeedbackUrlParams forClose(String triggerRecordId, String sendRecordId) {
		return new FeedbackUrlParams("close", triggerRecordId, sendRecordId);
	}

	/**
	 * 创建关闭类型的参数对象（包含用户信息）
	 */
	public static FeedbackUrlParams forClose(String triggerRecordId, String sendRecordId, String userId, String userName) {
		return new FeedbackUrlParams("close", triggerRecordId, sendRecordId, userId, userName);
	}

	@Override
	public String toString() {
		return "FeedbackUrlParams{" +
				"action='" + action + '\'' +
				", triggerRecordId='" + triggerRecordId + '\'' +
				", sendRecordId='" + sendRecordId + '\'' +
				", userId='" + userId + '\'' +
				", userName='" + userName + '\'' +
				'}';
	}
}
