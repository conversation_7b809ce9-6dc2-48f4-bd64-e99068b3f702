package com.github.cret.web.oee.document.feedback;

import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 快返触发记录表
 */
@Document("t_feedback_trigger_record")
public class FeedbackTriggerRecord {

	@Id
	private String id;

	// 异常编码
	@Field(name = "anomalies_code")
	private String anomaliesCode;

	// 异常名称
	@Field(name = "anomalies_name")
	private String anomaliesName;

	// 线体编码
	@Field(name = "line_code")
	private String lineCode;

	// 异常明细
	@Field(name = "anomalies_detail")
	private String anomaliesDetail;

	// 异常开始时间
	@Field(name = "anomalies_start_time")
	private Date anomaliesStartTime;

	// 异常结束时间
	@Field(name = "anomalies_end_time")
	private Date anomaliesEndTime;

	// 触发时间
	@Field(name = "trigger_time")
	private Date triggerTime;

	// 触发人
	@Field(name = "trigger_user_id")
	private String triggerUserId;

	// 触发名称
	@Field(name = "trigger_user_name")
	private String triggerUserName;

	// 异常是否关闭
	@Field(name = "trigger_close")
	private Boolean triggerClose;

	// 异常关闭时间
	@Field(name = "trigger_close_time")
	private Date triggerCloseTime;

	// 异常关闭人
	@Field(name = "trigger_close_user_id")
	private Date triggerCloseUserId;

	// 异常关闭人名称
	@Field(name = "trigger_close_user_name")
	private String triggerColseUserName;

	// 异常触发回调通知用户
	@Field(name = "notice_user")
	private List<NoticeUser> noticeUsers;

	// 解决方案记录
	@Field(name = "solutions")
	private FeedbackTriggerSolution[] solutions;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getAnomaliesCode() {
		return anomaliesCode;
	}

	public void setAnomaliesCode(String anomaliesCode) {
		this.anomaliesCode = anomaliesCode;
	}

	public String getAnomaliesName() {
		return anomaliesName;
	}

	public void setAnomaliesName(String anomaliesName) {
		this.anomaliesName = anomaliesName;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getAnomaliesDetail() {
		return anomaliesDetail;
	}

	public void setAnomaliesDetail(String anomaliesDetail) {
		this.anomaliesDetail = anomaliesDetail;
	}

	public Date getAnomaliesStartTime() {
		return anomaliesStartTime;
	}

	public void setAnomaliesStartTime(Date anomaliesStartTime) {
		this.anomaliesStartTime = anomaliesStartTime;
	}

	public Date getAnomaliesEndTime() {
		return anomaliesEndTime;
	}

	public void setAnomaliesEndTime(Date anomaliesEndTime) {
		this.anomaliesEndTime = anomaliesEndTime;
	}

	public Date getTriggerTime() {
		return triggerTime;
	}

	public void setTriggerTime(Date triggerTime) {
		this.triggerTime = triggerTime;
	}

	public String getTriggerUserId() {
		return triggerUserId;
	}

	public void setTriggerUserId(String triggerUserId) {
		this.triggerUserId = triggerUserId;
	}

	public String getTriggerUserName() {
		return triggerUserName;
	}

	public void setTriggerUserName(String triggerUserName) {
		this.triggerUserName = triggerUserName;
	}

	public Boolean getTriggerClose() {
		return triggerClose;
	}

	public void setTriggerClose(Boolean triggerClose) {
		this.triggerClose = triggerClose;
	}

	public Date getTriggerCloseTime() {
		return triggerCloseTime;
	}

	public void setTriggerCloseTime(Date triggerCloseTime) {
		this.triggerCloseTime = triggerCloseTime;
	}

	public Date getTriggerCloseUserId() {
		return triggerCloseUserId;
	}

	public void setTriggerCloseUserId(Date triggerCloseUserId) {
		this.triggerCloseUserId = triggerCloseUserId;
	}

	public String getTriggerColseUserName() {
		return triggerColseUserName;
	}

	public void setTriggerColseUserName(String triggerColseUserName) {
		this.triggerColseUserName = triggerColseUserName;
	}

	public List<NoticeUser> getNoticeUsers() {
		return noticeUsers;
	}

	public void setNoticeUsers(List<NoticeUser> noticeUsers) {
		this.noticeUsers = noticeUsers;
	}

	public FeedbackTriggerSolution[] getSolutions() {
		return solutions;
	}

	public void setSolutions(FeedbackTriggerSolution[] solutions) {
		this.solutions = solutions;
	}

}
