package com.github.cret.web.oee.service;

import java.util.List;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerRecord;
import com.github.cret.web.oee.document.feedback.NoticeUser;
import com.github.cret.web.oee.domain.query.FeedbackTriggerRecordQuery;

public interface FeedbackTriggerRecordService {

	FeedbackTriggerRecord save(FeedbackTriggerRecord record);

	void delete(String id);

	FeedbackTriggerRecord findById(String id);

	PageList<FeedbackTriggerRecord> search(FeedbackTriggerRecordQuery query);

	FeedbackTriggerRecord update(String id, FeedbackTriggerRecord record);

	boolean hasOpenExceptions(String lineCode);

	FeedbackTriggerRecord findLatestOpenException(String lineCode);

	FeedbackTriggerRecord closeException(String id);

	/**
	 * 获取触发记录的通知用户列表
	 * @param id 触发记录ID
	 * @return 通知用户列表
	 */
	List<NoticeUser> getNoticeUsers(String id);

}