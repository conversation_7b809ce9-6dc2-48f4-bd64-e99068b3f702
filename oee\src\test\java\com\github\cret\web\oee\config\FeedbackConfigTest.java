package com.github.cret.web.oee.config;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.github.cret.web.oee.domain.request.FeedbackUrlParams;
import com.github.cret.web.oee.utils.UrlEncryptionUtil;

/**
 * FeedbackConfig测试类
 */
class FeedbackConfigTest {

	private FeedbackConfig feedbackConfig;

	@BeforeEach
	void setUp() {
		feedbackConfig = new FeedbackConfig();
		feedbackConfig.setHandleBaseUrl("http://localhost:3333/feedback/handle");
		feedbackConfig.setEnableUrlEncryption(true);
		feedbackConfig.setUrlEncryptionKey("YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXoxMjM0NTY=");
	}

	@Test
	void testBuildFeedbackHandleUrlWithEncryption() {
		String url = feedbackConfig.buildFeedbackHandleUrl("edit", "trigger123", "send456");

		assertNotNull(url);
		assertTrue(url.startsWith("http://localhost:3333/feedback/handle?data="));

		// 提取加密数据并验证解密
		String encryptedData = url.substring(url.indexOf("?data=") + 6);
		Map<String, String> decryptedParams = feedbackConfig.decryptUrlParams(encryptedData);

		assertEquals("edit", decryptedParams.get("action"));
		assertEquals("trigger123", decryptedParams.get("triggerRecordId"));
		assertEquals("send456", decryptedParams.get("sendRecordId"));
	}

	@Test
	void testBuildFeedbackHandleUrlWithUserAndEncryption() {
		String url = feedbackConfig.buildFeedbackHandleUrlWithUser("view", "trigger123", "send456", "user789", "测试用户");

		assertNotNull(url);
		assertTrue(url.startsWith("http://localhost:3333/feedback/handle?data="));

		// 提取加密数据并验证解密
		String encryptedData = url.substring(url.indexOf("?data=") + 6);
		Map<String, String> decryptedParams = feedbackConfig.decryptUrlParams(encryptedData);

		assertEquals("view", decryptedParams.get("action"));
		assertEquals("trigger123", decryptedParams.get("triggerRecordId"));
		assertEquals("send456", decryptedParams.get("sendRecordId"));
		assertEquals("user789", decryptedParams.get("userId"));
		assertEquals("测试用户", decryptedParams.get("userName"));
	}

	@Test
	void testBuildFeedbackHandleUrlWithoutEncryption() {
		feedbackConfig.setEnableUrlEncryption(false);

		String url = feedbackConfig.buildFeedbackHandleUrl("edit", "trigger123", "send456");

		assertNotNull(url);
		assertEquals("http://localhost:3333/feedback/handle?action=edit&triggerRecordId=trigger123&sendRecordId=send456", url);
	}

	@Test
	void testBuildFeedbackHandleUrlWithUserWithoutEncryption() {
		feedbackConfig.setEnableUrlEncryption(false);

		String url = feedbackConfig.buildFeedbackHandleUrlWithUser("view", "trigger123", "send456", "user789", "测试用户");

		assertNotNull(url);
		assertTrue(url.startsWith("http://localhost:3333/feedback/handle?action=view&triggerRecordId=trigger123&sendRecordId=send456&userId=user789&userName="));
		assertTrue(url.contains("userName="));
	}

	@Test
	void testBuildFeedbackEditUrl() {
		String url = feedbackConfig.buildFeedbackEditUrl("trigger123", "send456");

		assertNotNull(url);
		assertTrue(url.startsWith("http://localhost:3333/feedback/handle?data="));

		// 验证action为edit
		String encryptedData = url.substring(url.indexOf("?data=") + 6);
		Map<String, String> decryptedParams = feedbackConfig.decryptUrlParams(encryptedData);
		assertEquals("edit", decryptedParams.get("action"));
	}

	@Test
	void testBuildFeedbackViewUrl() {
		String url = feedbackConfig.buildFeedbackViewUrl("trigger123", "send456");

		assertNotNull(url);
		assertTrue(url.startsWith("http://localhost:3333/feedback/handle?data="));

		// 验证action为view
		String encryptedData = url.substring(url.indexOf("?data=") + 6);
		Map<String, String> decryptedParams = feedbackConfig.decryptUrlParams(encryptedData);
		assertEquals("view", decryptedParams.get("action"));
	}

	@Test
	void testBuildFeedbackCloseUrl() {
		String url = feedbackConfig.buildFeedbackCloseUrl("trigger123", "send456");

		assertNotNull(url);
		assertTrue(url.startsWith("http://localhost:3333/feedback/handle?data="));

		// 验证action为close
		String encryptedData = url.substring(url.indexOf("?data=") + 6);
		Map<String, String> decryptedParams = feedbackConfig.decryptUrlParams(encryptedData);
		assertEquals("close", decryptedParams.get("action"));
	}

	@Test
	void testBuildFeedbackEditUrlWithUser() {
		String url = feedbackConfig.buildFeedbackEditUrlWithUser("trigger123", "send456", "user789", "测试用户");

		assertNotNull(url);
		assertTrue(url.startsWith("http://localhost:3333/feedback/handle?data="));

		// 验证所有参数
		String encryptedData = url.substring(url.indexOf("?data=") + 6);
		Map<String, String> decryptedParams = feedbackConfig.decryptUrlParams(encryptedData);
		assertEquals("edit", decryptedParams.get("action"));
		assertEquals("trigger123", decryptedParams.get("triggerRecordId"));
		assertEquals("send456", decryptedParams.get("sendRecordId"));
		assertEquals("user789", decryptedParams.get("userId"));
		assertEquals("测试用户", decryptedParams.get("userName"));
	}

	@Test
	void testBuildFeedbackViewUrlWithUser() {
		String url = feedbackConfig.buildFeedbackViewUrlWithUser("trigger123", "send456", "user789", "测试用户");

		assertNotNull(url);
		assertTrue(url.startsWith("http://localhost:3333/feedback/handle?data="));

		// 验证所有参数
		String encryptedData = url.substring(url.indexOf("?data=") + 6);
		Map<String, String> decryptedParams = feedbackConfig.decryptUrlParams(encryptedData);
		assertEquals("view", decryptedParams.get("action"));
		assertEquals("trigger123", decryptedParams.get("triggerRecordId"));
		assertEquals("send456", decryptedParams.get("sendRecordId"));
		assertEquals("user789", decryptedParams.get("userId"));
		assertEquals("测试用户", decryptedParams.get("userName"));
	}

	@Test
	void testEncryptionFallback() {
		// 使用无效密钥测试回退机制
		feedbackConfig.setUrlEncryptionKey("invalid-key");

		String url = feedbackConfig.buildFeedbackHandleUrl("edit", "trigger123", "send456");

		assertNotNull(url);
		assertEquals("http://localhost:3333/feedback/handle?action=edit&triggerRecordId=trigger123&sendRecordId=send456", url);
	}

	@Test
	void testIsUrlEncryptionEnabled() {
		assertTrue(feedbackConfig.isUrlEncryptionEnabled());

		feedbackConfig.setEnableUrlEncryption(false);
		assertFalse(feedbackConfig.isUrlEncryptionEnabled());

		feedbackConfig.setEnableUrlEncryption(true);
		feedbackConfig.setUrlEncryptionKey("invalid-key");
		assertFalse(feedbackConfig.isUrlEncryptionEnabled());
	}

	@Test
	void testDecryptUrlParams() {
		// 测试正常解密
		String secretKey = UrlEncryptionUtil.generateKey();
		feedbackConfig.setUrlEncryptionKey(secretKey);

		String url = feedbackConfig.buildFeedbackHandleUrlWithUser("edit", "trigger123", "send456", "user789", "测试用户");
		String encryptedData = url.substring(url.indexOf("?data=") + 6);

		Map<String, String> decryptedParams = feedbackConfig.decryptUrlParams(encryptedData);
		assertNotNull(decryptedParams);
		assertEquals(5, decryptedParams.size());

		// 测试解密失败情况
		feedbackConfig.setEnableUrlEncryption(false);
		Map<String, String> emptyParams = feedbackConfig.decryptUrlParams(encryptedData);
		assertNotNull(emptyParams);
		assertTrue(emptyParams.isEmpty());
	}

	@Test
	void testBuildFeedbackHandleUrlWithUserParams() {
		// 测试使用参数对象的方法
		FeedbackUrlParams params = FeedbackUrlParams.forView("trigger123", "send456", "user789", "测试用户");
		String url = feedbackConfig.buildFeedbackHandleUrlWithUser(params);

		assertNotNull(url);
		assertTrue(url.startsWith("http://localhost:3333/feedback/handle?data="));

		// 提取加密数据并验证解密
		String encryptedData = url.substring(url.indexOf("?data=") + 6);
		Map<String, String> decryptedParams = feedbackConfig.decryptUrlParams(encryptedData);

		assertEquals("view", decryptedParams.get("action"));
		assertEquals("trigger123", decryptedParams.get("triggerRecordId"));
		assertEquals("send456", decryptedParams.get("sendRecordId"));
		assertEquals("user789", decryptedParams.get("userId"));
		assertEquals("测试用户", decryptedParams.get("userName"));
	}

	@Test
	void testBuildFeedbackEditUrlWithUserParams() {
		// 测试使用参数对象的编辑URL方法
		FeedbackUrlParams params = FeedbackUrlParams.forEdit("trigger123", "send456", "user789", "测试用户");
		String url = feedbackConfig.buildFeedbackEditUrlWithUser(params);

		assertNotNull(url);
		assertTrue(url.startsWith("http://localhost:3333/feedback/handle?data="));

		// 验证所有参数
		String encryptedData = url.substring(url.indexOf("?data=") + 6);
		Map<String, String> decryptedParams = feedbackConfig.decryptUrlParams(encryptedData);
		assertEquals("edit", decryptedParams.get("action"));
		assertEquals("trigger123", decryptedParams.get("triggerRecordId"));
		assertEquals("send456", decryptedParams.get("sendRecordId"));
		assertEquals("user789", decryptedParams.get("userId"));
		assertEquals("测试用户", decryptedParams.get("userName"));
	}

	@Test
	void testBuildFeedbackViewUrlWithUserParams() {
		// 测试使用参数对象的查看URL方法
		FeedbackUrlParams params = FeedbackUrlParams.forView("trigger123", "send456", "user789", "测试用户");
		String url = feedbackConfig.buildFeedbackViewUrlWithUser(params);

		assertNotNull(url);
		assertTrue(url.startsWith("http://localhost:3333/feedback/handle?data="));

		// 验证所有参数
		String encryptedData = url.substring(url.indexOf("?data=") + 6);
		Map<String, String> decryptedParams = feedbackConfig.decryptUrlParams(encryptedData);
		assertEquals("view", decryptedParams.get("action"));
		assertEquals("trigger123", decryptedParams.get("triggerRecordId"));
		assertEquals("send456", decryptedParams.get("sendRecordId"));
		assertEquals("user789", decryptedParams.get("userId"));
		assertEquals("测试用户", decryptedParams.get("userName"));
	}

	@Test
	void testFeedbackUrlParamsStaticMethods() {
		// 测试静态工厂方法
		FeedbackUrlParams editParams = FeedbackUrlParams.forEdit("trigger123", "send456");
		assertEquals("edit", editParams.getAction());
		assertEquals("trigger123", editParams.getTriggerRecordId());
		assertEquals("send456", editParams.getSendRecordId());
		assertNull(editParams.getUserId());
		assertNull(editParams.getUserName());
		assertFalse(editParams.hasUserInfo());

		FeedbackUrlParams editParamsWithUser = FeedbackUrlParams.forEdit("trigger123", "send456", "user789", "测试用户");
		assertEquals("edit", editParamsWithUser.getAction());
		assertEquals("user789", editParamsWithUser.getUserId());
		assertEquals("测试用户", editParamsWithUser.getUserName());
		assertTrue(editParamsWithUser.hasUserInfo());

		FeedbackUrlParams viewParams = FeedbackUrlParams.forView("trigger123", "send456");
		assertEquals("view", viewParams.getAction());

		FeedbackUrlParams closeParams = FeedbackUrlParams.forClose("trigger123", "send456");
		assertEquals("close", closeParams.getAction());
	}
}
